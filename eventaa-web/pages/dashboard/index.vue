<template>
  <div class="bg-gray-100 min-h-screen">
    <div v-if="isLoading" class="flex justify-center items-center h-64">
      <div class="flex flex-col items-center">
        <CoreLoader color="#ef4444" />
        <p class="mt-4 text-gray-600">Loading dashboard...</p>
      </div>
    </div>

    <div v-else class="p-5">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-2xl font-bold">Welcome Back, {{ userName }}</h1>
          <p class="text-gray-600">{{ welcomeMessage }}</p>
        </div>
        <div class="flex space-x-2">
          <button v-if="hasHostRole" @click="$router.push('/dashboard/manage-events/create')"
            class="bg-red-600 text-white px-4 py-2 flex items-center hover:bg-red-700 transition-colors">
            <Icon icon="heroicons:plus" class="w-5 h-5 mr-1" />
            Create Event
          </button>
          <button v-if="hasAdminRole" @click="$router.push('/dashboard/users/invite')"
            class="bg-red-600 text-white px-4 py-2 flex items-center hover:bg-red-700 transition-colors">
            <Icon icon="heroicons:user-plus" class="w-5 h-5 mr-1" />
            Invite Member
          </button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <template v-if="isLoading">
          <DashboardSkeletonStatisticsCard v-for="i in 4" :key="i" />
        </template>
        <template v-else>
          <DashboardCardsStatistics
            title="Net Sales"
            :value="formatCurrency(stats.totalRevenue)"
            :subValue="`${formatCurrency(stats.grossSales)} gross sales`"
            icon="heroicons:currency-dollar"
            :growth="stats.revenueGrowth"
          />
          <DashboardCardsStatistics
            title="Tickets Sold"
            :value="`${stats.ticketsSold}/${stats.ticketsTotal}`"
            :subValue="`${stats.paidTickets} paid + ${stats.freeTickets} free`"
            icon="heroicons:ticket"
            :growth="stats.ticketsGrowth"
          />
          <DashboardCardsStatistics
            title="Page Views"
            :value="`${stats.pageViews}`"
            :subValue="`${stats.socialViews} from social media`"
            icon="heroicons:chart-bar"
            :growth="stats.viewsGrowth"
          />
          <DashboardCardsStatistics
            title="Active Users"
            :value="`${stats.activeUsers}`"
            :subValue="`${stats.newUsers} new this week`"
            icon="heroicons:users"
            :growth="stats.usersGrowth"
          />
        </template>
      </div>

      <!-- Admin-specific Stats -->
      <div v-if="hasAdminRole" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <template v-if="isLoading">
          <DashboardSkeletonStatisticsCard v-for="i in 4" :key="i" />
        </template>
        <template v-else>
          <DashboardCardsStatistics
            title="Vendors"
            :value="`${stats.adminStats.totalVendors}`"
            :subValue="`${stats.adminStats.pendingVendors} pending approval`"
            icon="heroicons:building-storefront"
            :growth="stats.adminStats.vendorsGrowth"
          />
          <DashboardCardsStatistics
            title="Venues"
            :value="`${stats.adminStats.totalVenues}`"
            :subValue="`${stats.adminStats.activeVenues} active`"
            icon="heroicons:building-office"
            :growth="stats.adminStats.venuesGrowth"
          />
          <DashboardCardsStatistics
            title="Total Events"
            :value="`${stats.adminStats.totalEvents}`"
            :subValue="`${stats.adminStats.upcomingEvents} upcoming`"
            icon="heroicons:calendar"
            :growth="stats.adminStats.eventsGrowth"
          />
          <DashboardCardsStatistics
            title="Platform Revenue"
            :value="formatCurrency(stats.adminStats.platformRevenue)"
            :subValue="`${stats.adminStats.subscriptions} subscriptions`"
            icon="heroicons:banknotes"
            :growth="stats.adminStats.revenueGrowth"
          />
        </template>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-4 shadow rounded-lg">
          <div class="flex justify-between items-center mb-4">
            <h2 class="font-semibold">{{ hasAdminRole ? 'Platform Revenue' : 'Event Revenue' }}</h2>
            <select v-model="chartPeriod" class="border-gray-300 rounded-md text-sm">
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="year">Last Year</option>
            </select>
          </div>
          <div class="h-64">
            <template v-if="isLoading">
              <DashboardSkeletonChart />
            </template>
            <template v-else>
              <DashboardChartsLine
                :data="revenueChartData"
                :options="chartOptions"
              />
            </template>
          </div>
        </div>
        <div class="bg-white p-4 shadow rounded-lg">
          <div class="flex justify-between items-center mb-4">
            <h2 class="font-semibold">{{ hasAdminRole ? 'User Growth' : 'Ticket Sales' }}</h2>
            <select v-model="chartPeriod" class="border-gray-300 rounded-md text-sm">
              <option value="7days">Last 7 Days</option>
              <option value="30days">Last 30 Days</option>
              <option value="90days">Last 90 Days</option>
              <option value="year">Last Year</option>
            </select>
          </div>
          <div class="h-64">
            <template v-if="isLoading">
              <DashboardSkeletonChart />
            </template>
            <template v-else>
              <DashboardChartsBar
                :data="secondaryChartData"
                :options="chartOptions"
              />
            </template>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white p-4 shadow mb-6">
        <h2 class="font-semibold mb-4">Quick Actions</h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <NuxtLink v-if="hasHostRole" to="/dashboard/manage-events" class="flex flex-col items-center p-4 border hover:bg-red-50 transition-colors">
            <div class="p-2 rounded-full bg-red-100 mb-2">
              <Icon icon="heroicons:calendar" class="h-6 w-6 text-red-600" />
            </div>
            <span class="text-sm font-medium text-center">Manage Events</span>
          </NuxtLink>

          <NuxtLink v-if="hasHostRole" to="/dashboard/tickets" class="flex flex-col items-center p-4 border hover:bg-red-50 transition-colors">
            <div class="p-2 rounded-full bg-red-100 mb-2">
              <Icon icon="heroicons:ticket" class="h-6 w-6 text-red-600" />
            </div>
            <span class="text-sm font-medium text-center">Tickets</span>
          </NuxtLink>

          <NuxtLink v-if="hasAdminRole" to="/dashboard/vendor-approval" class="flex flex-col items-center p-4 border hover:bg-red-50 transition-colors">
            <div class="p-2 rounded-full bg-red-100 mb-2">
              <Icon icon="heroicons:check-circle" class="h-6 w-6 text-red-600" />
            </div>
            <span class="text-sm font-medium text-center">Vendor Approval</span>
          </NuxtLink>

          <NuxtLink v-if="hasAdminRole" to="/dashboard/users" class="flex flex-col items-center p-4 border hover:bg-red-50 transition-colors">
            <div class="p-2 rounded-full bg-red-100 mb-2">
              <Icon icon="heroicons:user-group" class="h-6 w-6 text-red-600" />
            </div>
            <span class="text-sm font-medium text-center">Users</span>
          </NuxtLink>

          <NuxtLink to="/dashboard/reports" class="flex flex-col items-center p-4 border hover:bg-red-50 transition-colors">
            <div class="p-2 rounded-full bg-red-100 mb-2">
              <Icon icon="heroicons:document-chart-bar" class="h-6 w-6 text-red-600" />
            </div>
            <span class="text-sm font-medium text-center">Reports</span>
          </NuxtLink>

          <NuxtLink to="/dashboard/settings" class="flex flex-col items-center p-4 border hover:bg-red-50 transition-colors">
            <div class="p-2 rounded-full bg-red-100 mb-2">
              <Icon icon="heroicons:cog-6-tooth" class="h-6 w-6 text-red-600" />
            </div>
            <span class="text-sm font-medium text-center">Settings</span>
          </NuxtLink>
        </div>
      </div>

      <!-- Recent Activity Table -->
      <div class="bg-white p-4 shadow rounded-lg">
        <div class="flex justify-between items-center mb-4">
          <h2 class="font-semibold">{{ hasAdminRole ? 'Recent Platform Activity' : 'Recent Event Activity' }}</h2>
          <button class="text-red-600 text-sm font-medium hover:underline">View All</button>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b">
                <th class="p-2">{{ hasAdminRole ? 'User' : 'Attendee' }}</th>
                <th class="p-2">{{ hasAdminRole ? 'Action' : 'Ticket Type' }}</th>
                <th class="p-2">{{ hasAdminRole ? 'Resource' : 'Price' }}</th>
                <th class="p-2">{{ hasAdminRole ? 'Date' : 'Purchase Date' }}</th>
                <th class="p-2">Status</th>
              </tr>
            </thead>
            <tbody>
              <template v-if="isActivitiesLoading">
                <tr v-for="i in 4" :key="i" class="border-b">
                  <td v-for="j in 5" :key="j" class="p-2">
                    <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </td>
                </tr>
              </template>
              <template v-else-if="activities.length === 0">
                <tr class="border-b">
                  <td colspan="5" class="p-4 text-center text-gray-500">
                    No recent activity found
                  </td>
                </tr>
              </template>
              <template v-else>
                <tr class="border-b" v-for="(activity, index) in activities" :key="index">
                  <td class="p-2">{{ activity.user }}</td>
                  <td class="p-2">{{ activity.action }}</td>
                  <td class="p-2">{{ hasAdminRole ? activity.resource : formatCurrency(activity.price) }}</td>
                  <td class="p-2">{{ formatDate(activity.date) }}</td>
                  <td class="p-2">
                    <span :class="getStatusClass(activity.status)">
                      {{ activity.status }}
                    </span>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/store/auth';
import { ENDPOINTS } from '@/utils/api';

definePageMeta({
  layout: "dashboard",
  middleware: ['auth']
});

// Stores and utilities
const authStore = useAuthStore();
const httpClient = useHttpClient();
const { $toast, $echo }: any = useNuxtApp();

// State
const isLoading = ref(true);
const isActivitiesLoading = ref(true);
const activities = ref<any[]>([]);
const chartPeriod = ref('30days');
const revenueChartData = ref<any>({ labels: [], values: [] });
const secondaryChartData = ref<any>({ labels: [], values: [] });

// User information
const userName = computed(() => authStore.user?.name || 'User');

// Role checks
const hasAdminRole = computed(() => authStore.user?.roles?.includes('admin') || false);
const hasHostRole = computed(() => authStore.user?.roles?.includes('host') || false);

// Initialize stats with default values
const stats = ref<any>({
  totalRevenue: 0,
  grossSales: 0,
  revenueGrowth: 0,
  ticketsSold: 0,
  ticketsTotal: 0,
  paidTickets: 0,
  freeTickets: 0,
  ticketsGrowth: 0,
  pageViews: 0,
  socialViews: 0,
  viewsGrowth: 0,
  activeUsers: 0,
  newUsers: 0,
  usersGrowth: 0,
  adminStats: hasAdminRole.value ? {
    totalVendors: 0,
    pendingVendors: 0,
    vendorsGrowth: 0,
    totalVenues: 0,
    activeVenues: 0,
    venuesGrowth: 0,
    totalEvents: 0,
    upcomingEvents: 0,
    eventsGrowth: 0,
    platformRevenue: 0,
    subscriptions: 0,
    revenueGrowth: 0
  } : undefined
});

// Welcome message based on role
const welcomeMessage = computed(() => {
  if (hasAdminRole.value) {
    return 'Manage your platform and monitor overall performance';
  } else if (hasHostRole.value) {
    return 'Manage your events and track your performance';
  }
  return 'View your dashboard and manage your account';
});

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      type: 'linear' as const,
      beginAtZero: true,
      ticks: {
        callback: function(this: any, tickValue: string | number) {
          return hasAdminRole.value ? formatCurrency(Number(tickValue)) : tickValue;
        }
      }
    }
  },
  plugins: {
    legend: {
      display: false
    }
  }
};

// Format currency
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

const getStatusClass = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'approved':
    case 'active':
      return 'text-green-600';
    case 'pending':
    case 'in progress':
      return 'text-yellow-600';
    case 'cancelled':
    case 'rejected':
    case 'failed':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
};

const fetchDashboardData = async () => {
  try {
    isLoading.value = true;
    const endpoint = hasAdminRole.value ? ENDPOINTS.DASHBOARD.ADMIN.STATS : ENDPOINTS.DASHBOARD.HOST.STATS;
    const response = await httpClient.get<any>(endpoint);

    stats.value = response;
  } catch (error: any) {
    console.error('Error fetching dashboard data:', error);
    $toast.error('Failed to load dashboard data. Please try again later.');
  } finally {
    isLoading.value = false;
  }
};

const fetchActivities = async () => {
  try {
    isActivitiesLoading.value = true;
    const endpoint = hasAdminRole.value ? ENDPOINTS.DASHBOARD.ADMIN.ACTIVITIES : ENDPOINTS.DASHBOARD.HOST.ACTIVITIES;
    const response = await httpClient.get<any>(endpoint);
    activities.value = response;
  } catch (error) {
    console.error('Error fetching activities:', error);
    $toast.error('Failed to load activities');
  } finally {
    isActivitiesLoading.value = false;
  }
};

const fetchChartData = async () => {
  try {
    const [revenueResponse, secondaryResponse] = await Promise.all([
      httpClient.get<any>(
        hasAdminRole.value
          ? `${ENDPOINTS.DASHBOARD.ADMIN.REVENUE_CHART}?period=${chartPeriod.value}`
          : `${ENDPOINTS.DASHBOARD.HOST.REVENUE_CHART}?period=${chartPeriod.value}`
      ),
      httpClient.get<any>(
        hasAdminRole.value
          ? `${ENDPOINTS.DASHBOARD.ADMIN.USER_GROWTH_CHART}?period=${chartPeriod.value}`
          : `${ENDPOINTS.DASHBOARD.HOST.TICKET_SALES_CHART}?period=${chartPeriod.value}`
      )
    ]);

    revenueChartData.value = revenueResponse;
    secondaryChartData.value = secondaryResponse;
  } catch (error) {
    console.error('Error fetching chart data:', error);
    $toast.error('Failed to load chart data');
  }
};

watch(chartPeriod, () => {
  fetchChartData();
});

const setupWebSocketListeners = () => {
  if (!$echo) {
    console.warn('WebSocket connection not available');
    return;
  }

  try {
    const channel = hasAdminRole.value
      ? $echo.private('dashboard.admin')
      : $echo.private(`dashboard.host.${authStore.user?.id}`);

    channel.listen('.DashboardStatsUpdated', (event: any) => {
      if (event.stats) {
        stats.value = event.stats;
        $toast.info('Dashboard data updated');
      }
    });

    channel.error((error: any) => {
      console.error('WebSocket error:', error);
      $toast.error('Real-time updates unavailable');
    });
  } catch (error) {
    console.error('Error setting up WebSocket:', error);
  }
};

onMounted(async () => {
  await Promise.all([
    fetchDashboardData(),
    fetchActivities(),
    fetchChartData()
  ]);
  setupWebSocketListeners();
});

onUnmounted(() => {
  if ($echo) {
    const channelName = hasAdminRole.value
      ? 'dashboard.admin'
      : `dashboard.host.${authStore.user?.id}`;
    $echo.leave(channelName);
  }
});
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
