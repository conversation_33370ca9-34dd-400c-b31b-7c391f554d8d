<template>
    <div class="dashboard-bg-main min-h-screen">
        <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold dashboard-text-primary">Tickets</h1>
                        <p class="mt-2 dashboard-text-secondary">Manage and track your event tickets</p>
                    </div>
                </div>
            </div>

            <!-- Filters and Controls -->
            <div class="dashboard-bg-card dashboard-shadow-sm dashboard-border border mb-6 overflow-hidden">
                <div class="px-6 py-4 dashboard-border border-b bg-gray-50 dark:bg-zinc-900">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <!-- Event Selection and Search -->
                        <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                            <!-- Event Selector -->
                            <div class="relative min-w-[280px]">
                                <div v-if="eventsLoading" class="flex items-center space-x-2 py-2">
                                    <div class="animate-spin h-5 w-5 border-2 border-red-500 border-t-transparent"></div>
                                    <span class="dashboard-text-secondary">Loading events...</span>
                                </div>
                                <div v-else-if="events.length === 0"
                                    class="py-2 px-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200">
                                    <div class="flex items-center">
                                        <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 mr-2" />
                                        <span>No events found. Please create an event first.</span>
                                    </div>
                                </div>
                                <Combobox v-else v-model="selectedEvent">
                                    <div class="relative">
                                        <div class="relative w-full cursor-default overflow-hidden dashboard-border border dashboard-bg-input text-left sm:text-sm">
                                            <div class="absolute bg-red-600 text-white inset-y-0 left-0 flex items-center px-3">
                                                <Icon icon="fluent:ticket-diagonal-24-filled" class="w-5 h-5" />
                                            </div>
                                            <ComboboxInput v-model="query" placeholder="Search events..."
                                                class="w-full border-none py-2.5 pl-12 pr-10 dashboard-text-primary focus:ring-0 focus:outline-none bg-transparent"
                                                :display-value="(event: any) => event?.title || ''" />
                                            <ComboboxButton class="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <ChevronDownIcon class="h-5 w-5 dashboard-text-muted" aria-hidden="true" />
                                            </ComboboxButton>
                                        </div>

                                        <ComboboxOptions v-if="filteredEvents.length"
                                            class="absolute z-10 mt-1 max-h-60 w-full overflow-auto dashboard-bg-card py-1 text-base dashboard-shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-zinc-700 focus:outline-none sm:text-sm">
                                            <ComboboxOption v-for="event in filteredEvents" :key="event.id" :value="event"
                                                v-slot="{ active, selected }">
                                                <div :class="[
                                                    active ? 'bg-red-600 text-white' : 'dashboard-text-primary',
                                                    'relative cursor-default select-none py-2 pl-10 pr-4'
                                                ]">
                                                    <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                                                        {{ event.title }}
                                                    </span>
                                                    <span v-if="selected" :class="[
                                                        active ? 'text-white' : 'text-red-600',
                                                        'absolute inset-y-0 left-0 flex items-center pl-3'
                                                    ]">
                                                        <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                                    </span>
                                                </div>
                                            </ComboboxOption>
                                        </ComboboxOptions>

                                        <div v-else-if="query"
                                            class="absolute z-10 mt-1 w-full dashboard-bg-card py-2 px-3 dashboard-text-secondary text-sm dashboard-border border dashboard-shadow-lg">
                                            No events found matching "{{ query }}".
                                        </div>
                                    </div>
                                </Combobox>
                            </div>

                            <!-- Search Input -->
                            <div class="relative flex-grow max-w-md">
                                <input type="text" v-model="searchUuid" placeholder="Search by Ticket UUID"
                                    class="block w-full py-2.5 px-3 dashboard-input"
                                    @keyup.enter="searchTickets" />
                                <button @click="searchTickets"
                                    class="absolute inset-y-0 right-0 flex items-center px-3 dashboard-text-muted hover:text-red-600 dark:hover:text-red-500">
                                    <MagnifyingGlassIcon class="h-5 w-5" />
                                </button>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center space-x-3">
                            <!-- Filters -->
                            <Popover class="relative">
                                <PopoverButton class="dashboard-btn-secondary inline-flex items-center px-4 py-2 text-sm font-medium">
                                    <Icon icon="mynaui:filter-solid" class="w-5 h-5 mr-2" />
                                    Filters
                                </PopoverButton>

                                <transition enter-active-class="transition ease-out duration-200"
                                    enter-from-class="opacity-0 translate-y-1" enter-to-class="opacity-100 translate-y-0"
                                    leave-active-class="transition ease-in duration-150"
                                    leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 translate-y-1">
                                    <PopoverPanel class="absolute right-0 z-10 mt-2 w-96 origin-top-right dashboard-bg-card dashboard-shadow-lg ring-0 ring-black ring-opacity-5 dark:ring-zinc-700 focus:outline-none">
                                        <div class="p-6 space-y-4">
                                            <h4 class="text-lg font-semibold dashboard-text-primary">Filter Tickets</h4>

                                            <div class="space-y-4">
                                                <div>
                                                    <label class="block text-sm font-medium mb-2 dashboard-text-primary">Date Range</label>
                                                    <datepicker required position="left" @cleared="cleared"
                                                        placeholder="Select start & end date" :range="true" format="dd/MM/yyyy"
                                                        input-class-name="datepicker dashboard-input"
                                                        v-model="dateRange" />
                                                </div>

                                                <div>
                                                    <label class="block text-sm font-medium mb-2 dashboard-text-primary">Status</label>
                                                    <Listbox v-model="filters.status">
                                                        <div class="relative">
                                                            <ListboxButton class="relative w-full dashboard-input text-left sm:text-sm py-2.5 px-3">
                                                                <span>{{ statusLabel(filters.status) }}</span>
                                                            </ListboxButton>

                                                            <transition leave-active-class="transition ease-in duration-100"
                                                                leave-from-class="opacity-100" leave-to-class="opacity-0">
                                                                <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto dashboard-bg-card dashboard-shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-zinc-700 focus:outline-none sm:text-sm">
                                                                    <ListboxOption value="">
                                                                        <li class="cursor-pointer select-none relative py-2 px-4 hover:bg-gray-100 dark:hover:bg-zinc-700 dashboard-text-primary">
                                                                            All Statuses
                                                                        </li>
                                                                    </ListboxOption>
                                                                    <ListboxOption value="scanned">
                                                                        <li class="cursor-pointer select-none relative py-2 px-4 hover:bg-gray-100 dark:hover:bg-zinc-700 dashboard-text-primary">
                                                                            Scanned
                                                                        </li>
                                                                    </ListboxOption>
                                                                    <ListboxOption value="unscanned">
                                                                        <li class="cursor-pointer select-none relative py-2 px-4 hover:bg-gray-100 dark:hover:bg-zinc-700 dashboard-text-primary">
                                                                            Unscanned
                                                                        </li>
                                                                    </ListboxOption>
                                                                </ListboxOptions>
                                                            </transition>
                                                        </div>
                                                    </Listbox>
                                                </div>
                                            </div>

                                            <div class="flex justify-end space-x-3 pt-4 border-t dashboard-border">
                                                <button @click="resetFilters" class="dashboard-btn-secondary px-4 py-2 text-sm font-medium">
                                                    Reset
                                                </button>
                                                <button @click="applyFilters" class="dashboard-btn-primary px-4 py-2 text-sm font-medium">
                                                    Apply Filters
                                                </button>
                                            </div>
                                        </div>
                                    </PopoverPanel>
                                </transition>
                            </Popover>

                            <!-- Export Actions -->
                            <div class="flex items-center space-x-2">
                                <json-excel v-if="selectedEvent && formattedTickets.length > 0"
                                    class="dashboard-btn-secondary inline-flex items-center px-4 py-2 text-sm font-medium"
                                    :data="exportData" :fields="exportFields"
                                    :name="`tickets-${selectedEvent?.slug || 'event'}-${dayjs().format('YYYY-MM-DD')}.xlsx`"
                                    :before-generate="beforeExport" :before-finish="onExportDone">
                                    <img src="@/assets/icons/excel.png" alt="Excel" class="h-5 w-5 mr-2" />
                                    Excel
                                </json-excel>
                                <button v-else @click="$toast.error('Please select an event with tickets first')"
                                    class="dashboard-btn-secondary inline-flex items-center px-4 py-2 text-sm font-medium opacity-50 cursor-not-allowed">
                                    <img src="@/assets/icons/excel.png" alt="Excel" class="h-5 w-5 mr-2" />
                                    Excel
                                </button>
                                <button @click="downloadPdf"
                                    class="dashboard-btn-secondary inline-flex items-center px-4 py-2 text-sm font-medium">
                                    <img src="@/assets/icons/pdf.png" alt="PDF" class="h-5 w-5 mr-2" />
                                    PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tickets Table -->
            <div v-if="selectedEvent" class="dashboard-bg-card dashboard-shadow-sm dashboard-border border overflow-hidden">
                <!-- Table Header -->
                <div class="px-6 py-4 dashboard-border border-b bg-gray-50 dark:bg-zinc-900">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <h3 class="text-lg font-medium dashboard-text-primary">
                                {{ selectedEvent.title }} - Tickets
                            </h3>
                        </div>
                        <div class="text-sm dashboard-text-secondary">
                            {{ pagination.total }} {{ pagination.total === 1 ? 'ticket' : 'tickets' }}
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex justify-center items-center py-20">
                    <CoreLoader />
                </div>

                <!-- Table Content -->
                <div v-else-if="formattedTickets.length > 0" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
                        <thead class="bg-gray-100 dark:bg-zinc-900">
                            <tr>
                                <th v-for="header in headers" :key="header.value" scope="col"
                                    class="px-6 py-3 text-left text-xs font-bold dashboard-text-primary uppercase tracking-wider">
                                    {{ header.text }}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="dashboard-bg-card divide-y divide-gray-200 dark:divide-zinc-700">
                            <tr v-for="(ticket, index) in formattedTickets" :key="ticket.id"
                                class="hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors">

                                <!-- Ticket UUID -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium dashboard-text-primary">
                                        {{ ticket.uuid }}
                                    </div>
                                </td>

                                <!-- Customer -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm dashboard-text-primary">
                                        {{ ticket.customer }}
                                    </div>
                                </td>

                                <!-- Email -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm dashboard-text-primary">
                                        {{ ticket.email }}
                                    </div>
                                </td>

                                <!-- Purchase Date -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm dashboard-text-primary">
                                        {{ ticket.purchaseDate }}
                                    </div>
                                </td>

                                <!-- Status -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'inline-flex px-2 py-1 text-xs font-semibold',
                                        ticket.status === 'scanned'
                                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                    ]">
                                        {{ ticket.status === 'scanned' ? 'Scanned' : 'Unscanned' }}
                                    </span>
                                </td>

                                <!-- Actions -->
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button @click="scanTicket(ticket)" :disabled="ticket.status === 'scanned'"
                                        :class="[
                                            'inline-flex items-center px-3 py-1.5 text-xs font-medium transition-colors',
                                            ticket.status === 'scanned'
                                                ? 'bg-gray-100 dark:bg-zinc-800 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                                : 'bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
                                        ]"
                                        :title="ticket.status === 'scanned' ? 'Already scanned' : 'Scan ticket'">
                                        <QrCodeIcon class="h-4 w-4 mr-1" />
                                        {{ ticket.status === 'scanned' ? 'Scanned' : 'Scan' }}
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Empty State -->
                <div v-else class="text-center py-12">
                    <Icon icon="heroicons:ticket" class="w-12 h-12 dashboard-text-muted mx-auto mb-4" />
                    <h3 class="text-lg font-medium dashboard-text-primary mb-2">
                        No tickets found
                    </h3>
                    <p class="dashboard-text-secondary">
                        No tickets have been generated for this event yet.
                    </p>
                </div>

                <!-- Pagination -->
                <div v-if="!loading && formattedTickets.length > 0"
                    class="px-6 py-4 dashboard-border border-t bg-gray-50 dark:bg-zinc-900">
                    <div class="flex items-center justify-between">
                        <div class="text-sm dashboard-text-secondary">
                            Showing {{ pagination.from || 0 }} to {{ pagination.to || 0 }} of {{ pagination.total }} results
                        </div>
                        <div class="flex items-center space-x-2">
                            <button @click="previousPage" :disabled="pagination.current_page <= 1"
                                class="px-3 py-1 text-sm dashboard-border border dashboard-bg-card dashboard-text-primary hover:dashboard-bg-hover disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                Previous
                            </button>
                            <span class="text-sm dashboard-text-secondary">
                                {{ pagination.current_page }} of {{ pagination.last_page }}
                            </span>
                            <button @click="nextPage" :disabled="pagination.current_page >= pagination.last_page"
                                class="px-3 py-1 text-sm dashboard-border border dashboard-bg-card dashboard-text-primary hover:dashboard-bg-hover disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Event Selected State -->
            <div v-else class="dashboard-bg-card dashboard-shadow-sm dashboard-border border overflow-hidden">
                <div class="text-center py-12">
                    <Icon icon="heroicons:ticket" class="w-12 h-12 dashboard-text-muted mx-auto mb-4" />
                    <h3 class="text-lg font-medium dashboard-text-primary mb-2">
                        Select an Event
                    </h3>
                    <p class="dashboard-text-secondary">
                        Please select an event from the dropdown above to view its tickets.
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions,
    Listbox,
    ListboxButton,
    ListboxOption,
    ListboxOptions,
    Popover,
    PopoverButton,
    PopoverPanel
} from '@headlessui/vue';
import {
    ChevronDownIcon,
    CheckIcon,
    QrCodeIcon,
    MagnifyingGlassIcon,
} from '@heroicons/vue/20/solid';
import { ref, watch, onMounted, computed } from 'vue';
import type { EventItem } from '@/types';
import type { EventsResponse } from '@/types/api';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@/utils/dateformat';
import JsonExcel from 'vue3-json-excel';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

definePageMeta({
    middleware: ['auth'],
    layout: 'dashboard'
});

interface Ticket {
    id: number;
    uuid: string;
    user_ticket: any;
    email: string;
    purchaseDate: string;
    scanned: number;
    created_at: string;
}

interface PaginationData {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface TicketResponse {
    data: Ticket[];
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

const httpClient = useHttpClient();
const events = ref<EventItem[]>([]);
const { $toast }: any = useNuxtApp();
const currentEventPage = ref<number>(1);
const itemsPerPage = ref<number>(50);
const totalEvents = ref<number>(0);
const selectedEvent = ref<EventItem | null>(null);
const tickets = ref<Ticket[]>([]);
const loading = ref(false);
const eventsLoading = ref(true);
const searchUuid = ref('');
const query = ref('');
const dateRange = ref([]);

const cleared = () => {
    dateRange.value = [];
    filters.value.dateFrom = '';
    filters.value.dateTo = '';
};

const statusLabel = (status: string) => {
    if (status === 'scanned') return 'Scanned'
    if (status === 'unscanned') return 'Unscanned'
    return 'All Statuses'
}

const filteredEvents = computed(() => {
    if (!query.value) return events.value
    return events.value.filter(event =>
        event.title.toLowerCase().includes(query.value.toLowerCase())
    )
});

const formattedTickets = computed(() => {
    return tickets.value && tickets.value.length > 0 ?
        tickets.value.map((ticket) => {
            return {
                id: ticket.id,
                uuid: ticket.uuid,
                customer: ticket.user_ticket !== null ? ticket.user_ticket.name : "Not assigned",
                email: ticket.user_ticket !== null ? ticket.user_ticket.name : "Not assigned",
                purchaseDate: dayjs(ticket.created_at).format(DATE_FORMAT.FULL),
                status: ticket.scanned === 1 ? 'scanned' : 'unscanned',
            }
        }) : []
})

const pagination = ref<PaginationData>({
    current_page: 1,
    from: 0,
    last_page: 1,
    per_page: 50,
    to: 0,
    total: 0,
    links: []
});

const filters = ref({
    dateFrom: '',
    dateTo: '',
    status: ''
});

const headers = [
    { text: 'TICKET UUID', value: 'uuid' },
    { text: 'CUSTOMER', value: 'customer' },
    { text: 'EMAIL ADDRESS', value: 'email' },
    { text: 'PURCHASE DATE', value: 'purchaseDate' },
    { text: 'STATUS', value: 'status' },
    { text: 'ACTIONS', value: 'actions' }
];

const fetchTicketsForEvent = async (eventId: number, page = 1, searchQuery = '', filterParams = {}) => {
    if (!eventId) {
        $toast.error('No event selected');
        return [];
    }

    try {
        let url = `${ENDPOINTS.EVENTS.TICKETS}/${eventId}?page=${page}&per_page=${pagination.value.per_page}`;

        if (searchQuery) {
            url += `&search=${searchQuery}`;
        }

        if (filterParams) {
            Object.entries(filterParams).forEach(([key, value]) => {
                if (value) {
                    url += `&${key}=${value}`;
                }
            });
        }

        const response = await httpClient.get<TicketResponse>(url);

        if (response && response.data) {
            tickets.value = response.data;

            // Update pagination data
            Object.assign(pagination.value, {
                current_page: response.current_page || 1,
                from: response.from || 0,
                last_page: response.last_page || 1,
                per_page: response.per_page || pagination.value.per_page,
                to: response.to || 0,
                total: response.total || 0,
                links: response.links || []
            });

            return response.data;
        } else {
            tickets.value = [];
            return [];
        }
    } catch (error) {
        console.error('Error fetching tickets:', error);
        $toast.error('Failed to load tickets. Please try again.');
        tickets.value = [];
        return [];
    }
};

const loadTickets = async () => {
    if (!selectedEvent.value) {
        tickets.value = [];
        return;
    }

    try {
        loading.value = true;
        await fetchTicketsForEvent(selectedEvent.value.id, pagination.value.current_page);
    } catch (error) {
        console.error('Error loading tickets:', error);
        $toast.error('Failed to load tickets. Please try again.');
        tickets.value = [];
    } finally {
        loading.value = false;
    }
};

const onPageChange = async (page: number) => {
    if (page < 1 || page > pagination.value.last_page) return;

    if (!selectedEvent.value) return;
    const filterParams = {
        date_from: filters.value.dateFrom,
        date_to: filters.value.dateTo,
        status: filters.value.status
    };

    await fetchTicketsForEvent(selectedEvent.value.id, page, searchUuid.value, filterParams);
};

const previousPage = async () => {
    if (pagination.value.current_page > 1) {
        await onPageChange(pagination.value.current_page - 1);
    }
};

const nextPage = async () => {
    if (pagination.value.current_page < pagination.value.last_page) {
        await onPageChange(pagination.value.current_page + 1);
    }
};

const scanTicket = async (ticket: any) => {
    if (ticket.status === 'scanned') return;

    try {
        loading.value = true;
        await httpClient.post(`${ENDPOINTS.EVENTS.SEARCH}/${ticket.id}`);
        await loadTickets();

        $toast.success(`Ticket ${ticket.uuid} scanned successfully!`);
    } catch (error) {
        console.error('Error scanning ticket:', error);
        $toast.error('Failed to scan ticket. Please try again.');
    } finally {
        loading.value = false;
    }
};

const searchTickets = async () => {
    if (!selectedEvent.value) return;
    pagination.value.current_page = 1;
    const filterParams = {
        date_from: filters.value.dateFrom,
        date_to: filters.value.dateTo,
        status: filters.value.status
    };
    await fetchTicketsForEvent(selectedEvent.value.id, 1, searchUuid.value, filterParams);
};

const resetFilters = () => {
    filters.value = {
        dateFrom: '',
        dateTo: '',
        status: ''
    };
    dateRange.value = [];
};

const applyFilters = async () => {
    if (!selectedEvent.value) return;
    pagination.value.current_page = 1;
    const filterParams = {
        date_from: filters.value.dateFrom,
        date_to: filters.value.dateTo,
        status: filters.value.status
    };

    await fetchTicketsForEvent(selectedEvent.value.id, 1, searchUuid.value, filterParams);
};

const exportFields = computed(() => ({
    'Ticket UUID': 'uuid',
    'Customer': 'customer',
    'Email': 'email',
    'Purchase Date': 'purchaseDate',
    'Status': 'status'
}));

const exportData = computed(() => {
    return formattedTickets.value.map(ticket => ({
        uuid: ticket.uuid,
        customer: ticket.customer,
        email: ticket.email,
        purchaseDate: ticket.purchaseDate,
        status: ticket.status === 'scanned' ? 'Scanned' : 'Unscanned'
    }));
});

const beforeExport = (): void => {
    $toast.info('Preparing Excel export...');
};

const onExportDone = (): void => {
    $toast.success('Excel file exported successfully');
};



const downloadPdf = async () => {
    if (!selectedEvent.value) {
        $toast.error('Please select an event first');
        return;
    }

    if (formattedTickets.value.length === 0) {
        $toast.error('No tickets available to export');
        return;
    }

    try {
        $toast.info('Generating PDF...');

        const doc = new jsPDF();
        const tableColumn = ["Ticket UUID", "Customer", "Email", "Purchase Date", "Status"];
        const tableRows: any[] = [];

        formattedTickets.value.forEach(ticket => {
            const ticketData = [
                ticket.uuid,
                ticket.customer,
                ticket.email,
                ticket.purchaseDate,
                ticket.status === 'scanned' ? 'Scanned' : 'Unscanned'
            ];
            tableRows.push(ticketData);
        });

        doc.setFontSize(18);
        doc.setTextColor(220, 38, 38);
        doc.text('EventaHub', 14, 15);

        doc.setFontSize(15);
        doc.setTextColor(0, 0, 0);
        doc.text(`Tickets for: ${selectedEvent.value.title}`, 14, 25);

        doc.setFontSize(11);
        doc.setTextColor(100, 100, 100);
        doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY')} at ${dayjs().format('h:mm A')}`, 14, 32);
        doc.text(`Total Tickets: ${formattedTickets.value.length}`, 14, 38);

        if (filters.value.status) {
            doc.text(`Filter: ${statusLabel(filters.value.status)}`, 14, 44);
        }

        autoTable(doc, {
            head: [tableColumn],
            body: tableRows,
            startY: 50,
            theme: 'grid',
            styles: {
                fontSize: 10,
                cellPadding: 3,
                overflow: 'linebreak',
            },
            headStyles: {
                fillColor: [220, 38, 38],
                textColor: 255,
                fontStyle: 'bold',
            },
            alternateRowStyles: {
                fillColor: [245, 245, 245]
            }
        });

        doc.save(`tickets-${selectedEvent.value.slug}-${dayjs().format('YYYY-MM-DD')}.pdf`);
        $toast.success('PDF downloaded successfully!');
    } catch (error) {
        console.error('Error downloading PDF:', error);
        $toast.error('Failed to download PDF. Please try again.');
    }
};

const fetchEvents = async (): Promise<void> => {
    try {
        eventsLoading.value = true;
        const response = await httpClient.get<EventsResponse>(
            `${ENDPOINTS.EVENTS.USER}?per_page=${itemsPerPage.value}&page=${currentEventPage.value}`
        );

        if (response && response.events) {
            events.value = response.events.data || [];
            totalEvents.value = response.events.total || 0;

            if (events.value.length > 0 && !selectedEvent.value) {
                selectedEvent.value = events.value[0];
            }
        } else {
            events.value = [];
            totalEvents.value = 0;
            $toast.warning('No events found. Please create an event first.');
        }
    } catch (error) {
        events.value = [];
        totalEvents.value = 0;
        $toast.error('An error occurred while fetching events, please try again later.');
        console.error('Error fetching events:', error);
    } finally {
        eventsLoading.value = false;
    }
};

onMounted(async () => {
    try {
        await fetchEvents();
        if (selectedEvent.value) {
            await loadTickets();
        }
    } catch (error) {
        console.error('Error initializing tickets page:', error);
        $toast.error('Failed to initialize the tickets page. Please refresh and try again.');
    }
});

watch(selectedEvent, async () => {
    if (selectedEvent.value) {
        pagination.value.current_page = 1;
        await loadTickets();
    }
});

watch(dateRange, (newDateRange) => {
    if (newDateRange && Array.isArray(newDateRange) && newDateRange.length === 2) {
        filters.value.dateFrom = dayjs(newDateRange[0]).format('YYYY-MM-DD');
        filters.value.dateTo = dayjs(newDateRange[1]).format('YYYY-MM-DD');
    } else {
        filters.value.dateFrom = '';
        filters.value.dateTo = '';
    }
});
</script>

<style lang="css" scoped></style>
