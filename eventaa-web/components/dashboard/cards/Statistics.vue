<template>
  <div class="bg-white p-4 shadow flex flex-col items-center text-center">
    <Icon :icon="icon" class="w-10 h-10 text-blue-500" />
    <h2 class="text-lg font-semibold mt-2">{{ title }}</h2>
    <p class="text-xl font-bold">{{ value }}</p>
    <p class="text-gray-500 text-sm">{{ subValue }}</p>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";

defineProps<{
  title: string;
  value: string;
  subValue: string;
  icon: string;
}>();
</script>

<style></style>
